import { test, expect } from '@playwright/test';
import { MobileAuditRunner } from './src/audit-runner.js';
import { VIEWPORTS } from './src/config.js';

/**
 * JobbLogg Mobile Responsiveness Test Suite
 * Comprehensive mobile audit tests using Playwright
 */

// Global audit runner instance
let auditRunner: MobileAuditRunner;

test.beforeAll(async () => {
  auditRunner = new MobileAuditRunner();
});

test.describe('JobbLogg Mobile Responsiveness Audit', () => {
  
  test('Complete mobile audit across all routes and viewports', async () => {
    // Run complete audit
    const results = await auditRunner.runCompleteAudit();
    
    // Verify we got results for all viewport/route combinations
    expect(results.length).toBeGreaterThan(0);
    
    // Generate reports
    const reports = await auditRunner.generateReports(results);
    expect(reports.jsonPath).toBeTruthy();
    expect(reports.markdownPath).toBeTruthy();
    expect(reports.htmlPath).toBeTruthy();
    
    // Generate fix suggestions
    const patchFiles = await auditRunner.generateFixSuggestions(results);
    
    // Log summary
    const passedTests = results.filter(r => r.status === 'passed').length;
    const failedTests = results.filter(r => r.status === 'failed').length;
    const warningTests = results.filter(r => r.status === 'warning').length;
    
    console.log(`\n📊 Audit Summary:`);
    console.log(`  ✅ Passed: ${passedTests}`);
    console.log(`  ❌ Failed: ${failedTests}`);
    console.log(`  ⚠️  Warnings: ${warningTests}`);
    console.log(`  📄 Reports: ${Object.values(reports).length}`);
    console.log(`  🔧 Fix suggestions: ${patchFiles.length} files`);
    
    // Fail test if there are critical issues
    const criticalIssues = results.filter(r => r.status === 'failed').length;
    if (criticalIssues > 0) {
      console.log(`\n❌ ${criticalIssues} routes have critical mobile responsiveness issues`);
      console.log(`📝 Check the generated reports for detailed recommendations`);
    }
    
    // Don't fail the test - this is an audit, not a blocking test
    // expect(criticalIssues).toBe(0);
  });

  // Individual viewport tests for targeted debugging
  VIEWPORTS.forEach(viewport => {
    test(`Mobile audit for ${viewport.description}`, async () => {
      const results = await auditRunner.runViewportAudit(viewport.name);
      
      expect(results.length).toBeGreaterThan(0);
      
      const failedRoutes = results.filter(r => r.status === 'failed');
      
      if (failedRoutes.length > 0) {
        console.log(`\n❌ ${viewport.description} has issues on ${failedRoutes.length} routes:`);
        failedRoutes.forEach(result => {
          const totalIssues = result.touchTargetIssues.length + 
                            result.layoutIssues.length + 
                            result.accessibilityIssues.length;
          console.log(`  - ${result.routeName}: ${totalIssues} issues`);
        });
      }
      
      // Log viewport-specific metrics
      const avgLighthouseScore = results.reduce((sum, r) => sum + r.performanceMetrics.lighthouseScore, 0) / results.length;
      console.log(`📱 ${viewport.description} average Lighthouse score: ${Math.round(avgLighthouseScore)}/100`);
    });
  });

  // Critical route tests (high priority routes that must pass)
  const criticalRoutes = ['/dashboard', '/project/:projectId', '/shared/:sharedId'];
  
  criticalRoutes.forEach(routePath => {
    test(`Critical route audit: ${routePath}`, async () => {
      const results = await auditRunner.runRouteAudit(routePath);
      
      expect(results.length).toBe(VIEWPORTS.length);
      
      // Critical routes should not have any failed tests
      const failedViewports = results.filter(r => r.status === 'failed');
      
      if (failedViewports.length > 0) {
        console.log(`\n❌ Critical route ${routePath} failed on:`);
        failedViewports.forEach(result => {
          console.log(`  - ${result.viewport.description}: ${result.touchTargetIssues.length + result.layoutIssues.length + result.accessibilityIssues.length} issues`);
        });
      }
      
      // For critical routes, we might want to enforce stricter requirements
      // expect(failedViewports.length).toBe(0);
    });
  });

  // Performance-focused tests
  test('Mobile performance audit', async () => {
    const results = await auditRunner.runCompleteAudit();
    
    // Check Core Web Vitals across all tests
    const avgLCP = results.reduce((sum, r) => sum + r.performanceMetrics.lcp, 0) / results.length;
    const avgCLS = results.reduce((sum, r) => sum + r.performanceMetrics.cls, 0) / results.length;
    const avgLighthouseScore = results.reduce((sum, r) => sum + r.performanceMetrics.lighthouseScore, 0) / results.length;
    
    console.log(`\n⚡ Performance Metrics:`);
    console.log(`  LCP (Largest Contentful Paint): ${Math.round(avgLCP)}ms`);
    console.log(`  CLS (Cumulative Layout Shift): ${avgCLS.toFixed(3)}`);
    console.log(`  Average Lighthouse Score: ${Math.round(avgLighthouseScore)}/100`);
    
    // Performance thresholds (informational, not blocking)
    const goodLCP = avgLCP < 2500; // 2.5 seconds
    const goodCLS = avgCLS < 0.1;
    const goodLighthouse = avgLighthouseScore >= 80;
    
    console.log(`  LCP Status: ${goodLCP ? '✅' : '⚠️'} ${goodLCP ? 'Good' : 'Needs improvement'}`);
    console.log(`  CLS Status: ${goodCLS ? '✅' : '⚠️'} ${goodCLS ? 'Good' : 'Needs improvement'}`);
    console.log(`  Lighthouse Status: ${goodLighthouse ? '✅' : '⚠️'} ${goodLighthouse ? 'Good' : 'Needs improvement'}`);
  });

  // JobbLogg-specific compliance tests
  test('JobbLogg design system compliance', async () => {
    const results = await auditRunner.runCompleteAudit();
    
    const tokenCompliance = results.filter(r => r.usesJobbLoggTokens).length / results.length * 100;
    const responsiveCompliance = results.filter(r => r.hasResponsiveBreakpoints).length / results.length * 100;
    const norwegianSupport = results.filter(r => r.norwegianTextSupport).length / results.length * 100;
    
    console.log(`\n🎨 JobbLogg Design System Compliance:`);
    console.log(`  Design Token Usage: ${Math.round(tokenCompliance)}%`);
    console.log(`  Responsive Breakpoints: ${Math.round(responsiveCompliance)}%`);
    console.log(`  Norwegian Text Support: ${Math.round(norwegianSupport)}%`);
    
    // Compliance thresholds (informational)
    const goodTokenUsage = tokenCompliance >= 80;
    const goodResponsive = responsiveCompliance >= 70;
    const goodNorwegian = norwegianSupport >= 95;
    
    console.log(`  Token Status: ${goodTokenUsage ? '✅' : '⚠️'} ${goodTokenUsage ? 'Good' : 'Needs improvement'}`);
    console.log(`  Responsive Status: ${goodResponsive ? '✅' : '⚠️'} ${goodResponsive ? 'Good' : 'Needs improvement'}`);
    console.log(`  Norwegian Status: ${goodNorwegian ? '✅' : '⚠️'} ${goodNorwegian ? 'Good' : 'Needs improvement'}`);
  });

  // Touch target compliance test (WCAG AA requirement)
  test('Touch target WCAG AA compliance', async () => {
    const results = await auditRunner.runCompleteAudit();
    
    const touchTargetIssues = results.reduce((sum, r) => sum + r.touchTargetIssues.length, 0);
    const criticalTouchIssues = results.reduce((sum, r) => 
      sum + r.touchTargetIssues.filter(issue => issue.severity === 'critical').length, 0
    );
    
    console.log(`\n👆 Touch Target Compliance:`);
    console.log(`  Total touch target issues: ${touchTargetIssues}`);
    console.log(`  Critical touch target issues: ${criticalTouchIssues}`);
    
    if (touchTargetIssues > 0) {
      console.log(`\n⚠️  Touch target issues found. Common fixes:`);
      console.log(`  - Add 'touch-target' class to small buttons`);
      console.log(`  - Increase padding on interactive elements`);
      console.log(`  - Ensure minimum 44x44px touch area`);
    }
    
    // For WCAG AA compliance, critical touch target issues should be addressed
    // expect(criticalTouchIssues).toBe(0);
  });

  // Layout overflow test (mobile-specific)
  test('Mobile layout overflow prevention', async () => {
    const results = await auditRunner.runCompleteAudit();
    
    const layoutIssues = results.reduce((sum, r) => sum + r.layoutIssues.length, 0);
    const overflowIssues = results.reduce((sum, r) => 
      sum + r.layoutIssues.filter(issue => issue.type === 'horizontal_overflow').length, 0
    );
    
    console.log(`\n📱 Layout Overflow Analysis:`);
    console.log(`  Total layout issues: ${layoutIssues}`);
    console.log(`  Horizontal overflow issues: ${overflowIssues}`);
    
    if (overflowIssues > 0) {
      console.log(`\n⚠️  Horizontal overflow detected. Common fixes:`);
      console.log(`  - Add 'max-w-full overflow-hidden' classes`);
      console.log(`  - Replace fixed widths with responsive alternatives`);
      console.log(`  - Use 'min-w-0' to allow flex items to shrink`);
    }
    
    // Horizontal overflow is critical for mobile UX
    // expect(overflowIssues).toBe(0);
  });
});

// Utility test for debugging specific issues
test.describe('Debug Utilities', () => {
  
  test.skip('Debug specific route and viewport', async () => {
    // Uncomment and modify for debugging specific issues
    // const results = await auditRunner.runRouteAudit('/dashboard', 'ultra-narrow-mobile');
    // console.log('Debug results:', JSON.stringify(results, null, 2));
  });
  
  test.skip('Debug touch targets on narrow screens', async () => {
    // Uncomment for debugging touch target issues
    // const results = await auditRunner.runViewportAudit('ultra-narrow-mobile');
    // const touchIssues = results.flatMap(r => r.touchTargetIssues);
    // console.log('Touch target issues:', touchIssues);
  });
});
