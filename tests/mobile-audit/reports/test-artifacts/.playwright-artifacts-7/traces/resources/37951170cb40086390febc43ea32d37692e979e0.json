{"auth_config": {"object": "auth_config", "id": "aac_2yzrDxoAv5viAdZkvYBWzBmMq2I", "first_name": "off", "last_name": "off", "email_address": "on", "phone_number": "off", "username": "off", "password": "required", "identification_requirements": [["email_address", "oauth_apple", "oauth_google", "oauth_token_apple"], []], "identification_strategies": ["email_address", "oauth_apple", "oauth_google"], "first_factors": ["email_code", "oauth_apple", "oauth_google", "oauth_token_apple", "password", "reset_password_email_code", "ticket"], "second_factors": [], "email_address_verification_strategies": ["email_code"], "single_session_mode": true, "enhanced_email_deliverability": false, "test_mode": true, "cookieless_dev": true, "url_based_session_syncing": true, "claimed_at": null, "reverification": true}, "display_config": {"object": "display_config", "id": "display_config_2yzrDtwj3QdmtlaLo7QxCNa0rei", "instance_environment_type": "development", "application_name": "JobbLogg", "theme": {"buttons": {"font_color": "#ffffff", "font_family": "\"Source Sans Pro\", sans-serif", "font_weight": "600"}, "general": {"color": "#6c47ff", "padding": "1em", "box_shadow": "0 2px 8px rgba(0, 0, 0, 0.2)", "font_color": "#151515", "font_family": "\"Source Sans Pro\", sans-serif", "border_radius": "0.5em", "background_color": "#ffffff", "label_font_weight": "600"}, "accounts": {"background_color": "#ffffff"}}, "preferred_sign_in_strategy": "password", "logo_image_url": "", "favicon_image_url": "", "home_url": "http://localhost:5173", "sign_in_url": "https://loved-dory-86.accounts.dev/sign-in", "sign_up_url": "https://loved-dory-86.accounts.dev/sign-up", "user_profile_url": "https://loved-dory-86.accounts.dev/user", "waitlist_url": "https://loved-dory-86.accounts.dev/waitlist", "after_sign_in_url": "http://localhost:5173", "after_sign_up_url": "http://localhost:5173", "after_sign_out_one_url": "https://loved-dory-86.accounts.dev/sign-in/choose", "after_sign_out_all_url": "https://loved-dory-86.accounts.dev/sign-in", "after_switch_session_url": "http://localhost:5173", "after_join_waitlist_url": "http://localhost:5173", "organization_profile_url": "https://loved-dory-86.accounts.dev/organization", "create_organization_url": "https://loved-dory-86.accounts.dev/create-organization", "after_leave_organization_url": "http://localhost:5173", "after_create_organization_url": "http://localhost:5173", "logo_link_url": "http://localhost:5173", "support_email": null, "branded": true, "experimental_force_oauth_first": false, "clerk_js_version": "5", "show_devmode_warning": true, "google_one_tap_client_id": null, "help_url": null, "privacy_policy_url": null, "terms_url": null, "logo_url": null, "favicon_url": null, "logo_image": null, "favicon_image": null, "captcha_public_key": "0x4AAAAAAAWXJGBD7bONzLBd", "captcha_widget_type": "smart", "captcha_public_key_invisible": "0x4AAAAAAAFV93qQdS0ycilX", "captcha_provider": "turnstile", "captcha_oauth_bypass": []}, "user_settings": {"attributes": {"email_address": {"enabled": true, "required": true, "used_for_first_factor": true, "first_factors": ["email_code"], "used_for_second_factor": false, "second_factors": [], "verifications": ["email_code"], "verify_at_sign_up": true}, "phone_number": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "username": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "web3_wallet": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "first_name": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "last_name": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "password": {"enabled": true, "required": true, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "authenticator_app": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "ticket": {"enabled": true, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "backup_code": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}, "passkey": {"enabled": false, "required": false, "used_for_first_factor": false, "first_factors": [], "used_for_second_factor": false, "second_factors": [], "verifications": [], "verify_at_sign_up": false}}, "sign_in": {"second_factor": {"required": false}}, "sign_up": {"captcha_enabled": true, "captcha_widget_type": "smart", "custom_action_required": false, "progressive": true, "mode": "public", "legal_consent_enabled": false}, "restrictions": {"allowlist": {"enabled": false}, "blocklist": {"enabled": false}, "block_email_subaddresses": {"enabled": false}, "block_disposable_email_domains": {"enabled": false}, "ignore_dots_for_gmail_addresses": {"enabled": false}}, "username_settings": {"min_length": 4, "max_length": 64, "allow_extended_special_characters": false}, "actions": {"delete_self": true, "create_organization": true, "create_organizations_limit": null}, "attack_protection": {"user_lockout": {"enabled": true, "max_attempts": 100, "duration_in_minutes": 60}, "pii": {"enabled": true}, "email_link": {"require_same_client": true}}, "passkey_settings": {"allow_autofill": true, "show_sign_in_button": true}, "social": {"oauth_apple": {"enabled": true, "required": false, "authenticatable": true, "block_email_subaddresses": false, "strategy": "oauth_apple", "not_selectable": false, "deprecated": false, "name": "Apple", "logo_url": "https://img.clerk.com/static/apple.png"}, "oauth_google": {"enabled": true, "required": false, "authenticatable": true, "block_email_subaddresses": true, "strategy": "oauth_google", "not_selectable": false, "deprecated": false, "name": "Google", "logo_url": "https://img.clerk.com/static/google.png"}}, "password_settings": {"disable_hibp": false, "min_length": 0, "max_length": 0, "require_special_char": false, "require_numbers": false, "require_uppercase": false, "require_lowercase": false, "show_zxcvbn": false, "min_zxcvbn_strength": 0, "enforce_hibp_on_sign_in": true, "allowed_special_characters": "!\"#$%&'()*+,-./:;<=>?@[]^_`{|}~"}, "saml": {"enabled": false}, "enterprise_sso": {"enabled": false}}, "organization_settings": {"enabled": false, "max_allowed_memberships": 5, "actions": {"admin_delete": true}, "domains": {"enabled": false, "enrollment_modes": [], "default_role": ""}, "creator_role": "org:admin"}, "fraud_settings": {"object": "fraud_settings", "native": {"device_attestation_mode": "disabled"}}, "commerce_settings": {"billing": {"enabled": false, "has_paid_user_plans": false, "has_paid_org_plans": false}}, "api_keys_settings": {"enabled": false}, "maintenance_mode": false}