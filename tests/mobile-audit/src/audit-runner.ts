import { test, expect, <PERSON>, Browser } from '@playwright/test';
import { RouteDiscovery } from './route-discovery.js';
import { LayoutValidation, TouchTargetValidation, AccessibilityValidation, PerformanceValidation } from './validation-modules/index.js';
import { MobileAuditReportGenerator } from './report-generator.js';
import { AutoFixGenerator } from './auto-fix-generator.js';
import { AuditResult, ViewportConfig, RouteConfig } from './types.js';
import { VIEWPORTS, BASE_URL } from './config.js';

/**
 * Main Mobile Audit Runner
 * Orchestrates the complete mobile responsiveness audit process
 */

export class MobileAuditRunner {
  private routeDiscovery: RouteDiscovery;
  private layoutValidation: LayoutValidation;
  private touchTargetValidation: TouchTargetValidation;
  private accessibilityValidation: AccessibilityValidation;
  private performanceValidation: PerformanceValidation;
  private reportGenerator: MobileAuditReportGenerator;
  private autoFixGenerator: AutoFixGenerator;

  constructor() {
    this.routeDiscovery = new RouteDiscovery();
    this.layoutValidation = new LayoutValidation();
    this.touchTargetValidation = new TouchTargetValidation();
    this.accessibilityValidation = new AccessibilityValidation();
    this.performanceValidation = new PerformanceValidation();
    this.reportGenerator = new MobileAuditReportGenerator();
    this.autoFixGenerator = new AutoFixGenerator();
  }

  /**
   * Run complete mobile audit across all routes and viewports
   */
  async runCompleteAudit(): Promise<AuditResult[]> {
    console.log('🚀 Starting JobbLogg Mobile Responsiveness Audit...');
    
    // Discover all routes
    const routes = await this.routeDiscovery.discoverRoutes();
    console.log(`📍 Found ${routes.length} routes to test`);

    const results: AuditResult[] = [];

    // Test each route on each viewport
    for (const viewport of VIEWPORTS) {
      console.log(`📱 Testing viewport: ${viewport.description} (${viewport.width}×${viewport.height})`);
      
      for (const route of routes) {
        console.log(`  🔍 Testing route: ${route.name} (${route.path})`);
        
        try {
          const result = await this.auditRoute(route, viewport);
          results.push(result);
          
          const status = result.status === 'passed' ? '✅' : result.status === 'failed' ? '❌' : '⚠️';
          console.log(`    ${status} ${result.status} - ${result.touchTargetIssues.length + result.layoutIssues.length + result.accessibilityIssues.length} issues`);
        } catch (error) {
          console.error(`    ❌ Error testing ${route.name}:`, error);
          
          // Create error result
          results.push({
            routeName: route.name,
            url: route.testUrl,
            viewport,
            status: 'failed',
            touchTargetIssues: [],
            layoutIssues: [{
              type: 'test_error',
              severity: 'critical',
              element: 'PAGE',
              selector: 'body',
              description: `Test failed with error: ${error instanceof Error ? error.message : 'Unknown error'}`,
              recommendation: 'Check browser console and network connectivity'
            }],
            accessibilityIssues: [],
            performanceMetrics: {
              lcp: 0,
              fid: 0,
              cls: 0,
              fcp: 0,
              ttfb: 0,
              lighthouseScore: 0
            },
            usesJobbLoggTokens: false,
            hasResponsiveBreakpoints: false,
            norwegianTextSupport: false,
            testedAt: new Date().toISOString()
          });
        }
      }
    }

    console.log(`\n📊 Audit completed: ${results.length} tests run`);
    return results;
  }

  /**
   * Audit a specific route on a specific viewport
   */
  async auditRoute(route: RouteConfig, viewport: ViewportConfig): Promise<AuditResult> {
    const browser = await this.getBrowser();
    const context = await browser.newContext({
      viewport: { width: viewport.width, height: viewport.height },
      userAgent: viewport.userAgent,
      locale: 'nb-NO',
      timezoneId: 'Europe/Oslo',
      colorScheme: 'light'
    });

    const page = await context.newPage();

    try {
      // Navigate to the route
      await page.goto(route.testUrl, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });

      // Wait for page to be ready
      await page.waitForLoadState('domcontentloaded');
      await page.waitForTimeout(1000); // Allow for any animations/transitions

      // Run all validations
      const [touchTargetIssues, layoutIssues, accessibilityIssues, performanceMetrics] = await Promise.all([
        this.touchTargetValidation.validate(page, viewport),
        this.layoutValidation.validate(page, viewport),
        this.accessibilityValidation.validate(page, viewport),
        this.performanceValidation.validate(page, viewport)
      ]);

      // Check JobbLogg-specific compliance
      const usesJobbLoggTokens = await this.checkJobbLoggTokenUsage(page);
      const hasResponsiveBreakpoints = await this.checkResponsiveBreakpoints(page);
      const norwegianTextSupport = await this.checkNorwegianTextSupport(page);

      // Determine overall status
      const totalIssues = touchTargetIssues.length + layoutIssues.length + accessibilityIssues.length;
      const criticalIssues = [...touchTargetIssues, ...layoutIssues, ...accessibilityIssues]
        .filter(issue => issue.severity === 'critical').length;
      
      let status: 'passed' | 'failed' | 'warning';
      if (criticalIssues > 0) {
        status = 'failed';
      } else if (totalIssues > 5) {
        status = 'warning';
      } else {
        status = 'passed';
      }

      return {
        routeName: route.name,
        url: route.testUrl,
        viewport,
        status,
        touchTargetIssues,
        layoutIssues,
        accessibilityIssues,
        performanceMetrics,
        usesJobbLoggTokens,
        hasResponsiveBreakpoints,
        norwegianTextSupport,
        testedAt: new Date().toISOString()
      };

    } finally {
      await context.close();
    }
  }

  /**
   * Check if page uses JobbLogg design tokens
   */
  private async checkJobbLoggTokenUsage(page: Page): Promise<boolean> {
    return await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      let jobbLoggTokenCount = 0;
      let totalColorClasses = 0;

      elements.forEach(element => {
        const classes = element.className.toString();
        
        // Count JobbLogg tokens
        if (classes.includes('jobblogg-')) {
          jobbLoggTokenCount++;
        }
        
        // Count total color classes
        if (classes.match(/\b(bg-|text-|border-|ring-)/)) {
          totalColorClasses++;
        }
      });

      // Consider compliant if >50% of color classes use JobbLogg tokens
      return totalColorClasses === 0 || (jobbLoggTokenCount / totalColorClasses) > 0.5;
    });
  }

  /**
   * Check if page uses responsive breakpoints
   */
  private async checkResponsiveBreakpoints(page: Page): Promise<boolean> {
    return await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      let responsiveClassCount = 0;
      let totalElements = 0;

      elements.forEach(element => {
        const classes = element.className.toString();
        totalElements++;
        
        // Check for responsive breakpoint classes
        if (classes.match(/\b(xxs:|xs:|sm:|md:|lg:|xl:)/)) {
          responsiveClassCount++;
        }
      });

      // Consider compliant if >20% of elements have responsive classes
      return totalElements === 0 || (responsiveClassCount / totalElements) > 0.2;
    });
  }

  /**
   * Check Norwegian text support
   */
  private async checkNorwegianTextSupport(page: Page): Promise<boolean> {
    return await page.evaluate(() => {
      const norwegianChars = ['æ', 'ø', 'å', 'Æ', 'Ø', 'Å'];
      const textContent = document.body.textContent || '';
      
      // Check if page contains Norwegian characters
      const hasNorwegianText = norwegianChars.some(char => textContent.includes(char));
      
      if (!hasNorwegianText) {
        return true; // No Norwegian text to validate
      }
      
      // Check if font supports Norwegian characters (simplified check)
      const computedStyle = window.getComputedStyle(document.body);
      const fontFamily = computedStyle.fontFamily;
      
      // JobbLogg uses Inter which supports Norwegian
      return fontFamily.includes('Inter') || fontFamily.includes('system-ui');
    });
  }

  /**
   * Generate comprehensive reports
   */
  async generateReports(results: AuditResult[]): Promise<{
    jsonPath: string;
    markdownPath: string;
    htmlPath: string;
  }> {
    console.log('📝 Generating reports...');
    
    const [jsonPath, markdownPath, htmlPath] = await Promise.all([
      this.reportGenerator.generateJSON(results),
      this.reportGenerator.generateMarkdown(results),
      this.reportGenerator.generateHTML(results)
    ]);

    console.log(`✅ Reports generated:`);
    console.log(`  📄 JSON: ${jsonPath}`);
    console.log(`  📝 Markdown: ${markdownPath}`);
    console.log(`  🌐 HTML: ${htmlPath}`);

    return { jsonPath, markdownPath, htmlPath };
  }

  /**
   * Generate automated fix suggestions
   */
  async generateFixSuggestions(results: AuditResult[]): Promise<string[]> {
    console.log('🔧 Generating automated fix suggestions...');
    
    const suggestions = await this.autoFixGenerator.generateFixSuggestions(results);
    const patchFiles = await this.autoFixGenerator.generatePatchFiles(suggestions);
    
    console.log(`✅ Generated ${suggestions.length} fix suggestions in ${patchFiles.length} patch files`);
    patchFiles.forEach(file => console.log(`  📄 ${file}`));
    
    return patchFiles;
  }

  /**
   * Get browser instance (placeholder - would be provided by Playwright test runner)
   */
  private async getBrowser(): Promise<Browser> {
    // This would be provided by the Playwright test context
    // For now, return a placeholder
    throw new Error('Browser instance should be provided by Playwright test runner');
  }

  /**
   * Run audit for specific route (for targeted testing)
   */
  async runRouteAudit(routePath: string, viewportName?: string): Promise<AuditResult[]> {
    const routes = await this.routeDiscovery.discoverRoutes();
    const targetRoute = routes.find(r => r.path === routePath);
    
    if (!targetRoute) {
      throw new Error(`Route not found: ${routePath}`);
    }

    const viewports = viewportName 
      ? VIEWPORTS.filter(v => v.name === viewportName)
      : VIEWPORTS;

    const results: AuditResult[] = [];
    
    for (const viewport of viewports) {
      const result = await this.auditRoute(targetRoute, viewport);
      results.push(result);
    }

    return results;
  }

  /**
   * Run audit for specific viewport (for targeted testing)
   */
  async runViewportAudit(viewportName: string): Promise<AuditResult[]> {
    const viewport = VIEWPORTS.find(v => v.name === viewportName);
    
    if (!viewport) {
      throw new Error(`Viewport not found: ${viewportName}`);
    }

    const routes = await this.routeDiscovery.discoverRoutes();
    const results: AuditResult[] = [];
    
    for (const route of routes) {
      const result = await this.auditRoute(route, viewport);
      results.push(result);
    }

    return results;
  }
}
