#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { MobileAuditRunner } from './audit-runner.js';

/**
 * JobbLogg Mobile Audit CLI
 * Command-line interface for running mobile responsiveness audits
 */

async function main() {
  console.log(chalk.blue.bold('🚀 JobbLogg Mobile Responsiveness Audit'));
  console.log(chalk.gray('Comprehensive mobile testing framework\n'));

  const program = new Command();

  program
    .name('mobile-audit')
    .description('JobbLogg Mobile Responsiveness Audit Framework')
    .version('1.0.0');

  program
    .option('-e, --env <environment>', 'Environment to test (development|production)', 'development')
    .option('-r, --route <route>', 'Test specific route')
    .option('-v, --viewport <viewport>', 'Test specific viewport')
    .option('--no-reports', 'Skip report generation')
    .option('--no-fixes', 'Skip fix suggestion generation')
    .option('--output <directory>', 'Output directory for reports', './reports');

  program.parse();

  const options = program.opts();
  
  try {
    const auditRunner = new MobileAuditRunner();
    
    console.log(chalk.yellow('📋 Audit Configuration:'));
    console.log(`  Environment: ${chalk.cyan(options.env)}`);
    console.log(`  Route filter: ${chalk.cyan(options.route || 'all routes')}`);
    console.log(`  Viewport filter: ${chalk.cyan(options.viewport || 'all viewports')}`);
    console.log(`  Generate reports: ${chalk.cyan(options.reports ? 'yes' : 'no')}`);
    console.log(`  Generate fixes: ${chalk.cyan(options.fixes ? 'yes' : 'no')}`);
    console.log('');

    let results;

    // Run audit based on options
    if (options.route && options.viewport) {
      console.log(chalk.blue('🎯 Running targeted audit (specific route + viewport)'));
      results = await auditRunner.runRouteAudit(options.route, options.viewport);
    } else if (options.route) {
      console.log(chalk.blue('🎯 Running route-specific audit'));
      results = await auditRunner.runRouteAudit(options.route);
    } else if (options.viewport) {
      console.log(chalk.blue('🎯 Running viewport-specific audit'));
      results = await auditRunner.runViewportAudit(options.viewport);
    } else {
      console.log(chalk.blue('🎯 Running complete audit'));
      results = await auditRunner.runCompleteAudit();
    }

    // Display summary
    const passedTests = results.filter(r => r.status === 'passed').length;
    const failedTests = results.filter(r => r.status === 'failed').length;
    const warningTests = results.filter(r => r.status === 'warning').length;
    const totalIssues = results.reduce((sum, r) => 
      sum + r.touchTargetIssues.length + r.layoutIssues.length + r.accessibilityIssues.length, 0
    );

    console.log('\n' + chalk.blue.bold('📊 Audit Summary:'));
    console.log(`  ${chalk.green('✅ Passed:')} ${passedTests}`);
    console.log(`  ${chalk.red('❌ Failed:')} ${failedTests}`);
    console.log(`  ${chalk.yellow('⚠️  Warnings:')} ${warningTests}`);
    console.log(`  ${chalk.gray('🔍 Total Issues:')} ${totalIssues}`);

    // Issue breakdown
    const criticalIssues = results.reduce((sum, r) => 
      sum + [...r.touchTargetIssues, ...r.layoutIssues, ...r.accessibilityIssues]
        .filter(issue => issue.severity === 'critical').length, 0
    );
    const highIssues = results.reduce((sum, r) => 
      sum + [...r.touchTargetIssues, ...r.layoutIssues, ...r.accessibilityIssues]
        .filter(issue => issue.severity === 'high').length, 0
    );

    if (totalIssues > 0) {
      console.log('\n' + chalk.yellow.bold('🚨 Issue Breakdown:'));
      console.log(`  ${chalk.red('🔴 Critical:')} ${criticalIssues}`);
      console.log(`  ${chalk.orange('🟠 High:')} ${highIssues}`);
    }

    // Performance metrics
    const avgLighthouseScore = results.reduce((sum, r) => sum + r.performanceMetrics.lighthouseScore, 0) / results.length;
    const avgLCP = results.reduce((sum, r) => sum + r.performanceMetrics.lcp, 0) / results.length;
    const avgCLS = results.reduce((sum, r) => sum + r.performanceMetrics.cls, 0) / results.length;

    console.log('\n' + chalk.blue.bold('⚡ Performance Metrics:'));
    console.log(`  ${chalk.cyan('Lighthouse Score:')} ${Math.round(avgLighthouseScore)}/100`);
    console.log(`  ${chalk.cyan('LCP (Largest Contentful Paint):')} ${Math.round(avgLCP)}ms`);
    console.log(`  ${chalk.cyan('CLS (Cumulative Layout Shift):')} ${avgCLS.toFixed(3)}`);

    // JobbLogg compliance
    const tokenCompliance = results.filter(r => r.usesJobbLoggTokens).length / results.length * 100;
    const responsiveCompliance = results.filter(r => r.hasResponsiveBreakpoints).length / results.length * 100;
    const norwegianSupport = results.filter(r => r.norwegianTextSupport).length / results.length * 100;

    console.log('\n' + chalk.blue.bold('🎨 JobbLogg Design System Compliance:'));
    console.log(`  ${chalk.cyan('Design Token Usage:')} ${Math.round(tokenCompliance)}%`);
    console.log(`  ${chalk.cyan('Responsive Breakpoints:')} ${Math.round(responsiveCompliance)}%`);
    console.log(`  ${chalk.cyan('Norwegian Text Support:')} ${Math.round(norwegianSupport)}%`);

    // Generate reports
    if (options.reports) {
      console.log('\n' + chalk.blue.bold('📝 Generating Reports...'));
      const reports = await auditRunner.generateReports(results);
      
      console.log(`  ${chalk.green('✅ JSON Report:')} ${reports.jsonPath}`);
      console.log(`  ${chalk.green('✅ Markdown Report:')} ${reports.markdownPath}`);
      console.log(`  ${chalk.green('✅ HTML Report:')} ${reports.htmlPath}`);
    }

    // Generate fix suggestions
    if (options.fixes) {
      console.log('\n' + chalk.blue.bold('🔧 Generating Fix Suggestions...'));
      const patchFiles = await auditRunner.generateFixSuggestions(results);
      
      if (patchFiles.length > 0) {
        console.log(`  ${chalk.green('✅ Generated')} ${patchFiles.length} patch files:`);
        patchFiles.forEach(file => console.log(`    📄 ${file}`));
      } else {
        console.log(`  ${chalk.gray('ℹ️  No automated fixes available')}`);
      }
    }

    // Final recommendations
    if (criticalIssues > 0) {
      console.log('\n' + chalk.red.bold('🚨 Critical Issues Found!'));
      console.log(chalk.red('Please address critical mobile responsiveness issues before deployment.'));
      console.log(chalk.gray('Check the generated reports for detailed recommendations.'));
    } else if (totalIssues > 0) {
      console.log('\n' + chalk.yellow.bold('⚠️  Issues Found'));
      console.log(chalk.yellow('Consider addressing the identified issues to improve mobile UX.'));
    } else {
      console.log('\n' + chalk.green.bold('🎉 All Tests Passed!'));
      console.log(chalk.green('Your application meets mobile responsiveness standards.'));
    }

    // Exit with appropriate code
    process.exit(criticalIssues > 0 ? 1 : 0);

  } catch (error) {
    console.error('\n' + chalk.red.bold('❌ Audit Failed:'));
    console.error(chalk.red(error instanceof Error ? error.message : 'Unknown error'));
    
    if (error instanceof Error && error.stack) {
      console.error(chalk.gray('\nStack trace:'));
      console.error(chalk.gray(error.stack));
    }
    
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red.bold('\n❌ Unhandled Promise Rejection:'));
  console.error(chalk.red(reason));
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(chalk.red.bold('\n❌ Uncaught Exception:'));
  console.error(chalk.red(error.message));
  if (error.stack) {
    console.error(chalk.gray(error.stack));
  }
  process.exit(1);
});

// Run the CLI
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
