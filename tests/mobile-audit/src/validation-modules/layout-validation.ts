import { Page } from '@playwright/test';
import { ViewportConfig, LayoutIssue, ValidationModule } from '../types.js';
import { JOBBLOGG_PATTERNS } from '../config.js';

/**
 * Layout Validation Module
 * Detects horizontal overflow, fixed-width elements, and responsive design issues
 */

export class LayoutValidation implements ValidationModule {
  name = 'Layout Validation';
  description = 'Detects layout overflow, fixed widths, and responsive design issues';

  async validate(page: Page, viewport: ViewportConfig): Promise<LayoutIssue[]> {
    const issues: LayoutIssue[] = [];

    // Check for horizontal overflow
    const overflowIssues = await this.checkHorizontalOverflow(page, viewport);
    issues.push(...overflowIssues);

    // Check for fixed-width elements
    const fixedWidthIssues = await this.checkFixedWidthElements(page, viewport);
    issues.push(...fixedWidthIssues);

    // Check for missing responsive classes
    const responsiveIssues = await this.checkResponsiveClasses(page, viewport);
    issues.push(...responsiveIssues);

    // Check for layout breaks
    const layoutBreaks = await this.checkLayoutBreaks(page, viewport);
    issues.push(...layoutBreaks);

    return issues;
  }

  /**
   * Check for horizontal overflow that causes horizontal scrollbars
   */
  private async checkHorizontalOverflow(page: Page, viewport: ViewportConfig): Promise<LayoutIssue[]> {
    const issues: LayoutIssue[] = [];

    try {
      // Check if page has horizontal scrollbar
      const hasHorizontalScroll = await page.evaluate(() => {
        return document.documentElement.scrollWidth > window.innerWidth;
      });

      if (hasHorizontalScroll) {
        // Find elements that extend beyond viewport
        const overflowingElements = await page.evaluate(() => {
          const elements: Array<{
            selector: string;
            tagName: string;
            width: number;
            right: number;
            viewportWidth: number;
          }> = [];

          const allElements = document.querySelectorAll('*');
          const viewportWidth = window.innerWidth;

          allElements.forEach((element) => {
            const rect = element.getBoundingClientRect();
            if (rect.right > viewportWidth && rect.width > 0) {
              // Generate a selector for the element
              let selector = element.tagName.toLowerCase();
              if (element.id) selector += `#${element.id}`;
              if (element.className) {
                const classes = element.className.toString().split(' ').filter(c => c);
                if (classes.length > 0) selector += `.${classes.slice(0, 3).join('.')}`;
              }

              elements.push({
                selector,
                tagName: element.tagName,
                width: rect.width,
                right: rect.right,
                viewportWidth
              });
            }
          });

          return elements;
        });

        overflowingElements.forEach(element => {
          issues.push({
            type: 'horizontal_overflow',
            severity: 'high',
            element: element.tagName,
            selector: element.selector,
            description: `Element extends ${Math.round(element.right - element.viewportWidth)}px beyond viewport (${element.width}px wide)`,
            recommendation: 'Add responsive classes like max-w-full, overflow-hidden, or use flexible layouts',
            affectedViewports: [viewport.name]
          });
        });
      }
    } catch (error) {
      console.error('Error checking horizontal overflow:', error);
    }

    return issues;
  }

  /**
   * Check for elements with fixed widths that don't adapt to viewport
   */
  private async checkFixedWidthElements(page: Page, viewport: ViewportConfig): Promise<LayoutIssue[]> {
    const issues: LayoutIssue[] = [];

    try {
      const fixedWidthElements = await page.evaluate(() => {
        const elements: Array<{
          selector: string;
          tagName: string;
          computedWidth: string;
          actualWidth: number;
        }> = [];

        const allElements = document.querySelectorAll('*');

        allElements.forEach((element) => {
          const computed = window.getComputedStyle(element);
          const width = computed.width;
          
          // Check for fixed pixel widths that are problematic on mobile
          if (width && width.includes('px') && !width.includes('auto')) {
            const pixelValue = parseInt(width);
            // Flag fixed widths larger than viewport or common problematic values
            if (pixelValue > 300 || (pixelValue > 200 && element.tagName !== 'IMG')) {
              let selector = element.tagName.toLowerCase();
              if (element.id) selector += `#${element.id}`;
              if (element.className) {
                const classes = element.className.toString().split(' ').filter(c => c);
                if (classes.length > 0) selector += `.${classes.slice(0, 2).join('.')}`;
              }

              elements.push({
                selector,
                tagName: element.tagName,
                computedWidth: width,
                actualWidth: pixelValue
              });
            }
          }
        });

        return elements;
      });

      fixedWidthElements.forEach(element => {
        const severity = element.actualWidth > viewport.width ? 'critical' : 'medium';
        
        issues.push({
          type: 'fixed_width_element',
          severity,
          element: element.tagName,
          selector: element.selector,
          description: `Element has fixed width of ${element.computedWidth} which may not adapt to different screen sizes`,
          recommendation: 'Replace fixed width with responsive classes like w-full, max-w-*, or use percentage/viewport units',
          affectedViewports: [viewport.name]
        });
      });
    } catch (error) {
      console.error('Error checking fixed width elements:', error);
    }

    return issues;
  }

  /**
   * Check for missing responsive classes on important elements
   */
  private async checkResponsiveClasses(page: Page, viewport: ViewportConfig): Promise<LayoutIssue[]> {
    const issues: LayoutIssue[] = [];

    try {
      const elementsNeedingResponsive = await page.evaluate((breakpoints) => {
        const elements: Array<{
          selector: string;
          tagName: string;
          classes: string;
          hasResponsive: boolean;
        }> = [];

        // Check key interactive elements that should have responsive classes
        const selectors = [
          'button',
          'input',
          'textarea',
          'form',
          '.card',
          '.container',
          '[class*="grid"]',
          '[class*="flex"]'
        ];

        selectors.forEach(sel => {
          const elements_found = document.querySelectorAll(sel);
          elements_found.forEach(element => {
            const classes = element.className.toString();
            const hasResponsive = breakpoints.some((bp: string) => classes.includes(bp));
            
            if (!hasResponsive && classes.length > 0) {
              let selector = element.tagName.toLowerCase();
              if (element.id) selector += `#${element.id}`;
              if (element.className) {
                const classNames = element.className.toString().split(' ').filter(c => c);
                if (classNames.length > 0) selector += `.${classNames.slice(0, 2).join('.')}`;
              }

              elements.push({
                selector,
                tagName: element.tagName,
                classes,
                hasResponsive
              });
            }
          });
        });

        return elements;
      }, JOBBLOGG_PATTERNS.BREAKPOINTS);

      elementsNeedingResponsive.forEach(element => {
        issues.push({
          type: 'responsive_missing',
          severity: 'medium',
          element: element.tagName,
          selector: element.selector,
          description: `Element lacks responsive breakpoint classes for mobile adaptation`,
          recommendation: `Add responsive classes using JobbLogg breakpoints: ${JOBBLOGG_PATTERNS.BREAKPOINTS.join(', ')}`,
          affectedViewports: [viewport.name]
        });
      });
    } catch (error) {
      console.error('Error checking responsive classes:', error);
    }

    return issues;
  }

  /**
   * Check for layout breaks and overlapping elements
   */
  private async checkLayoutBreaks(page: Page, viewport: ViewportConfig): Promise<LayoutIssue[]> {
    const issues: LayoutIssue[] = [];

    try {
      const layoutProblems = await page.evaluate(() => {
        const problems: Array<{
          type: string;
          selector: string;
          description: string;
        }> = [];

        // Check for elements that are cut off or invisible
        const allElements = document.querySelectorAll('*');
        
        allElements.forEach(element => {
          const rect = element.getBoundingClientRect();
          const computed = window.getComputedStyle(element);
          
          // Skip if element is intentionally hidden
          if (computed.display === 'none' || computed.visibility === 'hidden') return;
          
          // Check if element is cut off at bottom of viewport
          if (rect.bottom > window.innerHeight && rect.height > 0) {
            let selector = element.tagName.toLowerCase();
            if (element.id) selector += `#${element.id}`;
            
            problems.push({
              type: 'layout_break',
              selector,
              description: `Element extends ${Math.round(rect.bottom - window.innerHeight)}px below viewport`
            });
          }
          
          // Check for zero-height containers that might indicate layout collapse
          if (rect.height === 0 && element.children.length > 0) {
            let selector = element.tagName.toLowerCase();
            if (element.id) selector += `#${element.id}`;
            
            problems.push({
              type: 'layout_break',
              selector,
              description: 'Container has zero height despite having child elements'
            });
          }
        });

        return problems;
      });

      layoutProblems.forEach(problem => {
        issues.push({
          type: 'layout_break',
          severity: 'medium',
          element: problem.selector.split(/[#.]/)[0],
          selector: problem.selector,
          description: problem.description,
          recommendation: 'Review layout structure and ensure proper responsive design patterns',
          affectedViewports: [viewport.name]
        });
      });
    } catch (error) {
      console.error('Error checking layout breaks:', error);
    }

    return issues;
  }
}
