import { Page } from '@playwright/test';
import { ViewportConfig, TouchTargetIssue, ValidationModule } from '../types.js';
import { ACCESSIBILITY_THRESHOLDS } from '../config.js';

/**
 * Touch Target Validation Module
 * Validates WCAG AA touch target requirements (44x44px minimum)
 */

export class TouchTargetValidation implements ValidationModule {
  name = 'Touch Target Validation';
  description = 'Validates WCAG AA touch target size and spacing requirements';

  async validate(page: Page, viewport: ViewportConfig): Promise<TouchTargetIssue[]> {
    const issues: TouchTargetIssue[] = [];

    // Only run touch target validation on touch-enabled devices
    if (!viewport.hasTouch) {
      return issues;
    }

    // Check interactive element sizes
    const sizeIssues = await this.checkTouchTargetSizes(page, viewport);
    issues.push(...sizeIssues);

    // Check spacing between touch targets
    const spacingIssues = await this.checkTouchTargetSpacing(page, viewport);
    issues.push(...spacingIssues);

    // Check for overlapping touch targets
    const overlapIssues = await this.checkTouchTargetOverlap(page, viewport);
    issues.push(...overlapIssues);

    return issues;
  }

  /**
   * Check if interactive elements meet minimum touch target size requirements
   */
  private async checkTouchTargetSizes(page: Page, viewport: ViewportConfig): Promise<TouchTargetIssue[]> {
    const issues: TouchTargetIssue[] = [];

    try {
      const touchTargets = await page.evaluate((minSize) => {
        const targets: Array<{
          selector: string;
          tagName: string;
          width: number;
          height: number;
          x: number;
          y: number;
          isVisible: boolean;
          hasPointerEvents: boolean;
        }> = [];

        // Interactive elements that should meet touch target requirements
        const interactiveSelectors = [
          'button',
          'a',
          'input[type="button"]',
          'input[type="submit"]',
          'input[type="reset"]',
          'input[type="checkbox"]',
          'input[type="radio"]',
          '[role="button"]',
          '[onclick]',
          '[ontouch]',
          '.btn',
          '.button',
          '[class*="clickable"]',
          '[class*="touch"]'
        ];

        interactiveSelectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          
          elements.forEach((element, index) => {
            const rect = element.getBoundingClientRect();
            const computed = window.getComputedStyle(element);
            
            // Skip hidden elements
            if (computed.display === 'none' || computed.visibility === 'hidden' || rect.width === 0 || rect.height === 0) {
              return;
            }

            // Check if element has pointer events
            const hasPointerEvents = computed.pointerEvents !== 'none';
            
            // Generate selector
            let elementSelector = element.tagName.toLowerCase();
            if (element.id) {
              elementSelector += `#${element.id}`;
            } else if (element.className) {
              const classes = element.className.toString().split(' ').filter(c => c && !c.includes('hover') && !c.includes('focus'));
              if (classes.length > 0) {
                elementSelector += `.${classes.slice(0, 2).join('.')}`;
              }
            } else {
              elementSelector += `:nth-of-type(${index + 1})`;
            }

            targets.push({
              selector: elementSelector,
              tagName: element.tagName,
              width: rect.width,
              height: rect.height,
              x: rect.x,
              y: rect.y,
              isVisible: rect.width > 0 && rect.height > 0,
              hasPointerEvents
            });
          });
        });

        return targets;
      }, ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_TARGET);

      touchTargets.forEach(target => {
        if (!target.hasPointerEvents || !target.isVisible) return;

        const tooSmall = target.width < ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_TARGET || 
                        target.height < ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_TARGET;

        if (tooSmall) {
          // Determine severity based on how small the target is
          let severity: 'critical' | 'high' | 'medium' | 'low' = 'medium';
          const minDimension = Math.min(target.width, target.height);
          
          if (minDimension < 24) severity = 'critical';
          else if (minDimension < 32) severity = 'high';
          else if (minDimension < 40) severity = 'medium';

          // Generate recommendation based on current size
          let recommendation = `Increase touch target size to minimum ${ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_TARGET}x${ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_TARGET}px. `;
          
          if (viewport.name === 'ultra-narrow-mobile') {
            recommendation += 'For ultra-narrow screens, consider using touch-target-compact class (36x36px minimum).';
          } else {
            recommendation += 'Use touch-target utility class or add padding to increase clickable area.';
          }

          issues.push({
            type: 'touch_target_too_small',
            severity,
            element: target.tagName,
            selector: target.selector,
            dimensions: { width: target.width, height: target.height },
            position: { x: target.x, y: target.y },
            recommendation,
            wcagReference: 'WCAG 2.1 AA - Target Size (2.5.5)'
          });
        }
      });
    } catch (error) {
      console.error('Error checking touch target sizes:', error);
    }

    return issues;
  }

  /**
   * Check spacing between adjacent touch targets
   */
  private async checkTouchTargetSpacing(page: Page, viewport: ViewportConfig): Promise<TouchTargetIssue[]> {
    const issues: TouchTargetIssue[] = [];

    try {
      const spacingIssues = await page.evaluate((minSpacing) => {
        const problems: Array<{
          selector1: string;
          selector2: string;
          distance: number;
          element1: { x: number; y: number; width: number; height: number };
          element2: { x: number; y: number; width: number; height: number };
        }> = [];

        // Get all interactive elements
        const interactiveElements = Array.from(document.querySelectorAll(
          'button, a, input[type="button"], input[type="submit"], [role="button"], .btn, .button'
        ));

        // Check spacing between each pair of elements
        for (let i = 0; i < interactiveElements.length; i++) {
          for (let j = i + 1; j < interactiveElements.length; j++) {
            const elem1 = interactiveElements[i];
            const elem2 = interactiveElements[j];
            
            const rect1 = elem1.getBoundingClientRect();
            const rect2 = elem2.getBoundingClientRect();
            
            // Skip if either element is not visible
            if (rect1.width === 0 || rect1.height === 0 || rect2.width === 0 || rect2.height === 0) continue;
            
            // Calculate distance between elements
            const distance = Math.min(
              Math.abs(rect1.right - rect2.left),
              Math.abs(rect2.right - rect1.left),
              Math.abs(rect1.bottom - rect2.top),
              Math.abs(rect2.bottom - rect1.top)
            );
            
            // Check if elements are close enough to be problematic
            if (distance < minSpacing && distance >= 0) {
              // Generate selectors
              const selector1 = elem1.id ? `#${elem1.id}` : elem1.tagName.toLowerCase();
              const selector2 = elem2.id ? `#${elem2.id}` : elem2.tagName.toLowerCase();
              
              problems.push({
                selector1,
                selector2,
                distance,
                element1: { x: rect1.x, y: rect1.y, width: rect1.width, height: rect1.height },
                element2: { x: rect2.x, y: rect2.y, width: rect2.width, height: rect2.height }
              });
            }
          }
        }

        return problems;
      }, ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_SPACING);

      spacingIssues.forEach(problem => {
        const severity = problem.distance < 4 ? 'high' : 'medium';
        
        issues.push({
          type: 'touch_target_spacing',
          severity,
          element: `${problem.selector1} + ${problem.selector2}`,
          selector: `${problem.selector1}, ${problem.selector2}`,
          dimensions: { width: 0, height: 0 }, // Not applicable for spacing
          position: { x: problem.element1.x, y: problem.element1.y },
          recommendation: `Increase spacing between touch targets to minimum ${ACCESSIBILITY_THRESHOLDS.MIN_TOUCH_SPACING}px. Current spacing: ${Math.round(problem.distance)}px`,
          wcagReference: 'WCAG 2.1 AA - Target Size (2.5.5)'
        });
      });
    } catch (error) {
      console.error('Error checking touch target spacing:', error);
    }

    return issues;
  }

  /**
   * Check for overlapping touch targets
   */
  private async checkTouchTargetOverlap(page: Page, viewport: ViewportConfig): Promise<TouchTargetIssue[]> {
    const issues: TouchTargetIssue[] = [];

    try {
      const overlaps = await page.evaluate(() => {
        const problems: Array<{
          selector1: string;
          selector2: string;
          overlapArea: number;
          element1: { x: number; y: number; width: number; height: number };
          element2: { x: number; y: number; width: number; height: number };
        }> = [];

        const interactiveElements = Array.from(document.querySelectorAll(
          'button, a, input[type="button"], input[type="submit"], [role="button"], .btn, .button'
        ));

        // Check for overlapping elements
        for (let i = 0; i < interactiveElements.length; i++) {
          for (let j = i + 1; j < interactiveElements.length; j++) {
            const elem1 = interactiveElements[i];
            const elem2 = interactiveElements[j];
            
            const rect1 = elem1.getBoundingClientRect();
            const rect2 = elem2.getBoundingClientRect();
            
            // Skip if either element is not visible
            if (rect1.width === 0 || rect1.height === 0 || rect2.width === 0 || rect2.height === 0) continue;
            
            // Check for overlap
            const overlapLeft = Math.max(rect1.left, rect2.left);
            const overlapRight = Math.min(rect1.right, rect2.right);
            const overlapTop = Math.max(rect1.top, rect2.top);
            const overlapBottom = Math.min(rect1.bottom, rect2.bottom);
            
            if (overlapLeft < overlapRight && overlapTop < overlapBottom) {
              const overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop);
              
              // Generate selectors
              const selector1 = elem1.id ? `#${elem1.id}` : elem1.tagName.toLowerCase();
              const selector2 = elem2.id ? `#${elem2.id}` : elem2.tagName.toLowerCase();
              
              problems.push({
                selector1,
                selector2,
                overlapArea,
                element1: { x: rect1.x, y: rect1.y, width: rect1.width, height: rect1.height },
                element2: { x: rect2.x, y: rect2.y, width: rect2.width, height: rect2.height }
              });
            }
          }
        }

        return problems;
      });

      overlaps.forEach(overlap => {
        const severity = overlap.overlapArea > 100 ? 'critical' : 'high';
        
        issues.push({
          type: 'touch_target_overlap',
          severity,
          element: `${overlap.selector1} + ${overlap.selector2}`,
          selector: `${overlap.selector1}, ${overlap.selector2}`,
          dimensions: { width: 0, height: 0 }, // Not applicable for overlap
          position: { x: overlap.element1.x, y: overlap.element1.y },
          recommendation: `Resolve overlapping touch targets. Overlap area: ${Math.round(overlap.overlapArea)}px². Adjust positioning or z-index.`,
          wcagReference: 'WCAG 2.1 AA - Target Size (2.5.5)'
        });
      });
    } catch (error) {
      console.error('Error checking touch target overlap:', error);
    }

    return issues;
  }
}
